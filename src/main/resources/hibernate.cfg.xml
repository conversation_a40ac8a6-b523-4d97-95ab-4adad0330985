<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">

<hibernate-configuration>
    <session-factory>
        <!-- Database connection settings -->
        <property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
        <property name="hibernate.connection.url">*********************************************************************************************************</property>
        <property name="hibernate.connection.username">root</property>
        <property name="hibernate.connection.password">1234</property>
        
        <!-- Connection pool settings (HikariCP) -->
        <property name="hibernate.connection.provider_class">org.hibernate.hikaricp.internal.HikariCPConnectionProvider</property>
        <property name="hibernate.hikari.minimumIdle">5</property>
        <property name="hibernate.hikari.maximumPoolSize">20</property>
        <property name="hibernate.hikari.idleTimeout">300000</property>
        <property name="hibernate.hikari.connectionTimeout">20000</property>
        <property name="hibernate.hikari.maxLifetime">1200000</property>
        <property name="hibernate.hikari.leakDetectionThreshold">60000</property>
        
        <!-- SQL dialect -->
        <property name="hibernate.dialect">org.hibernate.dialect.MySQLDialect</property>
        
        <!-- Security and Performance settings -->
        <property name="hibernate.show_sql">false</property>
        <property name="hibernate.format_sql">false</property>
        <property name="hibernate.use_sql_comments">false</property>
        <property name="hibernate.hbm2ddl.auto">validate</property>
        
        <!-- Transaction settings -->
        <property name="hibernate.current_session_context_class">thread</property>
        <property name="hibernate.transaction.coordinator_class">jdbc</property>
        
        <!-- Cache settings -->
        <property name="hibernate.cache.use_second_level_cache">false</property>
        <property name="hibernate.cache.use_query_cache">false</property>
        
        <!-- Batch processing -->
        <property name="hibernate.jdbc.batch_size">25</property>
        <property name="hibernate.order_inserts">true</property>
        <property name="hibernate.order_updates">true</property>
        <property name="hibernate.jdbc.batch_versioned_data">true</property>
        
        <!-- Security settings -->
        <property name="hibernate.connection.isolation">2</property>
        <property name="hibernate.jdbc.use_get_generated_keys">true</property>
        
        <!-- Entity mappings -->
        <mapping class="DTO.Account"/>
        <mapping class="DTO.Computer"/>
        <mapping class="DTO.ComputerUsage"/>
        <mapping class="DTO.Employee"/>
        <mapping class="DTO.Invoice"/>
        <mapping class="DTO.InvoiceDetail"/>
        <mapping class="DTO.Message"/>
        <mapping class="DTO.Product"/>
        <mapping class="DTO.Session"/>
    </session-factory>
</hibernate-configuration>
