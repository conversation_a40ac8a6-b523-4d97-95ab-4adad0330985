package DTO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder

public class Computer implements Serializable {
    @Serial
    private static final long serialVersionUID = 746559035L;
    public enum ComputerType {
        Vip,
        Normal,
        ;
        @Override
        public String toString() {
            return switch (this){
                case Vip -> "Máy Vip";
                case Normal -> "<PERSON><PERSON><PERSON> thường";
            };
        }
    }
    public  enum ComputerStatus {
        MAINTAINING,
        LOCKED,
        OFF,
        USING,
        ;

        @Override
        public String toString() {
            return switch (this) {
                case MAINTAINING -> "đang bảo trì";
                case LOCKED -> "đang khóa";
                case OFF -> "đang tắt";
                case USING -> "đang dùng";
            };
        }
    }
    private int id;

    private String name;
    private double price;// giá tiền trên 1 giờ
    private ComputerType type;
    private ComputerStatus status = ComputerStatus.OFF;
    private Date createdAt = new Date();
    private Date deletedAt = null;

    private List<ComputerUsage> computerUsages;
    private List<Invoice> invoices;
    private Session currentSession;
    public void setStatus(Integer status) {
        this.status = ComputerStatus.values()[status];
    }
    public void setType(Integer type) {
        this.type = ComputerType.values()[type];
    }
    public void setType(String type) {
        if (type == null || type.trim().isEmpty()) {
            this.type = ComputerType.Normal; // Default to Normal
            return;
        }

        switch (type.trim()) {
            case "Máy VIP", "Máy Vip", "VIP", "Vip" -> this.type = ComputerType.Vip;
            case "Máy thường", "Máy thuong", "Normal", "normal", "Thường", "thuong" -> this.type = ComputerType.Normal;
            default -> {
                System.err.println("WARNING: Unknown computer type: '" + type + "'. Using Normal as default.");
                this.type = ComputerType.Normal;
            }
        }
    }

}
