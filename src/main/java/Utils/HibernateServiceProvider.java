package Utils;

import Repository.*;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Service Provider cho Hibernate repositories với singleton pattern
 * <PERSON><PERSON>ản lý lifecycle của các repository instances
 */
public class HibernateServiceProvider {
    private static final Logger logger = Logger.getLogger(HibernateServiceProvider.class.getName());
    private static HibernateServiceProvider instance;
    private final Map<Class<?>, Object> services = new HashMap<>();
    
    // Thread-safe singleton
    private static final Object lock = new Object();
    
    private HibernateServiceProvider() {
        initializeRepositories();
    }
    
    /**
     * Lấy singleton instance
     */
    public static HibernateServiceProvider getInstance() {
        if (instance == null) {
            synchronized (lock) {
                if (instance == null) {
                    instance = new HibernateServiceProvider();
                }
            }
        }
        return instance;
    }
    
    /**
     * Khởi tạo tất cả repositories
     */
    private void initializeRepositories() {
        try {
            logger.info("Initializing Hibernate repositories...");
            
            // Khởi tạo repositories
            services.put(AccountRepository.class, new AccountRepository());
            services.put(ComputerRepository.class, new ComputerRepository());
            // TODO: Thêm các repositories khác khi đã tạo
            
            logger.info("Hibernate repositories initialized successfully");
            
        } catch (Exception e) {
            logger.severe("Failed to initialize Hibernate repositories: " + e.getMessage());
            throw new RuntimeException("Repository initialization failed", e);
        }
    }
    
    /**
     * Lấy repository theo class type
     */
    @SuppressWarnings("unchecked")
    public <T> T getRepository(Class<T> repositoryClass) {
        Object repository = services.get(repositoryClass);
        if (repository == null) {
            throw new IllegalArgumentException("Repository not found: " + repositoryClass.getName());
        }
        return (T) repository;
    }
    
    /**
     * Đăng ký repository mới
     */
    public <T> void registerRepository(Class<T> repositoryClass, T repository) {
        services.put(repositoryClass, repository);
        logger.info("Registered repository: " + repositoryClass.getName());
    }
    
    /**
     * Kiểm tra repository đã được đăng ký chưa
     */
    public boolean isRepositoryRegistered(Class<?> repositoryClass) {
        return services.containsKey(repositoryClass);
    }
    
    /**
     * Lấy tất cả repository classes đã đăng ký
     */
    public java.util.Set<Class<?>> getRegisteredRepositories() {
        return services.keySet();
    }
    
    /**
     * Shutdown tất cả repositories và Hibernate
     */
    public void shutdown() {
        try {
            logger.info("Shutting down Hibernate Service Provider...");
            
            // Clear repositories
            services.clear();
            
            // Shutdown Hibernate
            HibernateUtil.shutdown();
            
            logger.info("Hibernate Service Provider shutdown completed");
            
        } catch (Exception e) {
            logger.severe("Error during Hibernate Service Provider shutdown: " + e.getMessage());
        }
    }
    
    /**
     * Khởi tạo static method để sử dụng trong main
     */
    public static void init() {
        getInstance();
        logger.info("Hibernate Service Provider initialized");
    }
    
    /**
     * Health check cho repositories
     */
    public boolean healthCheck() {
        try {
            // Kiểm tra Hibernate SessionFactory
            if (!HibernateUtil.isSessionFactoryOpen()) {
                logger.warning("Hibernate SessionFactory is not open");
                return false;
            }
            
            // Kiểm tra repositories
            if (services.isEmpty()) {
                logger.warning("No repositories registered");
                return false;
            }
            
            logger.info("Hibernate Service Provider health check passed");
            return true;
            
        } catch (Exception e) {
            logger.severe("Hibernate Service Provider health check failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Log thống kê repositories
     */
    public void logStatistics() {
        logger.info("=== Hibernate Service Provider Statistics ===");
        logger.info("Total repositories registered: " + services.size());
        logger.info("SessionFactory status: " + (HibernateUtil.isSessionFactoryOpen() ? "OPEN" : "CLOSED"));
        
        for (Class<?> repoClass : services.keySet()) {
            logger.info("Repository: " + repoClass.getSimpleName());
        }
        
        HibernateUtil.logConnectionPoolStats();
    }
}
