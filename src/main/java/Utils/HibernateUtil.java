package Utils;

import org.hibernate.SessionFactory;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.hibernate.cfg.Configuration;
import org.hibernate.service.ServiceRegistry;
import lombok.extern.java.Log;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Hibernate Utility class với singleton pattern và connection pooling an toàn
 * Sử dụng HikariCP để quản lý connection pool hiệu quả
 */
public class HibernateUtil {
    private static final Logger logger = Logger.getLogger(HibernateUtil.class.getName());
    private static SessionFactory sessionFactory;
    private static ServiceRegistry serviceRegistry;
    
    // Thread-safe singleton initialization
    private static final Object lock = new Object();
    
    static {
        try {
            buildSessionFactory();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Initial SessionFactory creation failed", e);
            throw new ExceptionInInitializerError(e);
        }
    }
    
    /**
     * Tạo SessionFactory với c<PERSON><PERSON> hình an toàn
     */
    private static void buildSessionFactory() {
        try {
            logger.info("Building Hibernate SessionFactory...");
            
            // Tạo configuration từ hibernate.cfg.xml
            Configuration configuration = new Configuration();
            configuration.configure("hibernate.cfg.xml");
            
            // Thêm các entity classes
            addEntityClasses(configuration);
            
            // Tạo service registry
            serviceRegistry = new StandardServiceRegistryBuilder()
                    .applySettings(configuration.getProperties())
                    .build();
            
            // Tạo session factory
            sessionFactory = configuration.buildSessionFactory(serviceRegistry);
            
            logger.info("Hibernate SessionFactory created successfully");
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to create SessionFactory", e);
            if (serviceRegistry != null) {
                StandardServiceRegistryBuilder.destroy(serviceRegistry);
            }
            throw e;
        }
    }
    
    /**
     * Thêm các entity classes vào configuration
     */
    private static void addEntityClasses(Configuration configuration) {
        configuration.addAnnotatedClass(DTO.Account.class);
        configuration.addAnnotatedClass(DTO.Computer.class);
        configuration.addAnnotatedClass(DTO.ComputerUsage.class);
        configuration.addAnnotatedClass(DTO.Employee.class);
        configuration.addAnnotatedClass(DTO.Invoice.class);
        configuration.addAnnotatedClass(DTO.InvoiceDetail.class);
        configuration.addAnnotatedClass(DTO.Message.class);
        configuration.addAnnotatedClass(DTO.Product.class);
        configuration.addAnnotatedClass(DTO.Session.class);
    }
    
    /**
     * Lấy SessionFactory instance (thread-safe)
     */
    public static SessionFactory getSessionFactory() {
        if (sessionFactory == null || sessionFactory.isClosed()) {
            synchronized (lock) {
                if (sessionFactory == null || sessionFactory.isClosed()) {
                    buildSessionFactory();
                }
            }
        }
        return sessionFactory;
    }
    
    /**
     * Lấy current session (thread-local)
     */
    public static org.hibernate.Session getCurrentSession() {
        return getSessionFactory().getCurrentSession();
    }
    
    /**
     * Mở session mới
     */
    public static org.hibernate.Session openSession() {
        return getSessionFactory().openSession();
    }
    
    /**
     * Đóng SessionFactory khi shutdown application
     */
    public static void shutdown() {
        try {
            logger.info("Shutting down Hibernate SessionFactory...");
            
            if (sessionFactory != null && !sessionFactory.isClosed()) {
                sessionFactory.close();
            }
            
            if (serviceRegistry != null) {
                StandardServiceRegistryBuilder.destroy(serviceRegistry);
            }
            
            logger.info("Hibernate SessionFactory shutdown completed");
            
        } catch (Exception e) {
            logger.log(Level.WARNING, "Error during Hibernate shutdown", e);
        }
    }
    
    /**
     * Kiểm tra trạng thái SessionFactory
     */
    public static boolean isSessionFactoryOpen() {
        return sessionFactory != null && !sessionFactory.isClosed();
    }
    
    /**
     * Thống kê connection pool (chỉ để debug)
     */
    public static void logConnectionPoolStats() {
        if (isSessionFactoryOpen()) {
            try {
                // Log basic stats
                logger.info("SessionFactory is active and ready");
            } catch (Exception e) {
                logger.log(Level.WARNING, "Could not retrieve connection pool stats", e);
            }
        }
    }
}
