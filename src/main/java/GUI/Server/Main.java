package GUI.Server;

import Io.Server;
import Io.SocketController;
import Utils.*;

import java.io.IOException;
import java.util.logging.Logger;

public class Main {
    private static final Logger logger = Logger.getLogger(Main.class.getName());

    public static void main(String[] args) throws IOException {
        try {
            logger.info("Starting Cyber-CF Server with Hibernate...");

            // Initialize UI
            Helper.initUI();

            // Initialize Hibernate Service Provider
            HibernateServiceProvider.init();

            // Perform migration if needed
            HibernateMigrationHelper migrationHelper = new HibernateMigrationHelper();
            migrationHelper.checkAndMigrate();
            migrationHelper.verifyMigration();

            // Initialize legacy service provider (for backward compatibility)
            ServiceProvider.init();

            // Setup shutdown hook for proper cleanup
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("Shutting down Cyber-CF Server...");
                HibernateServiceProvider.getInstance().shutdown();
            }));

            // Start socket server
            var socketServer = Server.initInstance(Constants.SOCKET_PORT);
            SocketController socketController = new SocketController(socketServer);
            socketController.startListen();

            // Show login GUI
            new LoginGUI();

            logger.info("Cyber-CF Server started successfully");

        } catch (Exception e) {
            logger.severe("Failed to start Cyber-CF Server: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
