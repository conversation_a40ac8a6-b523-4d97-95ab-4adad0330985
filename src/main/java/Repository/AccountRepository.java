package Repository;

import DTO.Account;
import org.hibernate.query.Query;

import java.util.List;
import java.util.Optional;

/**
 * Repository cho Account entity với các operations đặc biệt
 */
public class AccountRepository extends BaseRepository<Account, Integer> {
    
    public AccountRepository() {
        super(Account.class);
    }
    
    /**
     * Tìm account theo username
     */
    public Optional<Account> findByUsername(String username) {
        return executeWithSession(session -> {
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.username = :username AND a.deletedAt IS NULL", Account.class);
            query.setParameter("username", username);
            return query.uniqueResultOptional();
        });
    }
    
    /**
     * Tìm account theo username và password (cho login)
     */
    public Optional<Account> findByUsernameAndPassword(String username, String password) {
        return executeWithSession(session -> {
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.username = :username AND a.password = :password AND a.deletedAt IS NULL", 
                Account.class);
            query.setParameter("username", username);
            query.setParameter("password", password);
            return query.uniqueResultOptional();
        });
    }
    
    /**
     * Tìm accounts theo role
     */
    public List<Account> findByRole(Account.Role role) {
        return executeWithSession(session -> {
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.role = :role AND a.deletedAt IS NULL ORDER BY a.createdAt DESC", 
                Account.class);
            query.setParameter("role", role);
            return query.getResultList();
        });
    }
    
    /**
     * Tìm accounts có balance lớn hơn amount
     */
    public List<Account> findByBalanceGreaterThan(double amount) {
        return executeWithSession(session -> {
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.balance > :amount AND a.deletedAt IS NULL ORDER BY a.balance DESC", 
                Account.class);
            query.setParameter("amount", amount);
            return query.getResultList();
        });
    }
    
    /**
     * Cập nhật balance của account
     */
    public void updateBalance(Integer accountId, double newBalance) {
        executeWithTransaction(session -> {
            Query<?> query = session.createQuery(
                "UPDATE Account a SET a.balance = :balance WHERE a.id = :id");
            query.setParameter("balance", newBalance);
            query.setParameter("id", accountId);
            return query.executeUpdate();
        });
    }
    
    /**
     * Thêm tiền vào balance
     */
    public void addBalance(Integer accountId, double amount) {
        executeWithTransaction(session -> {
            Query<?> query = session.createQuery(
                "UPDATE Account a SET a.balance = a.balance + :amount WHERE a.id = :id");
            query.setParameter("amount", amount);
            query.setParameter("id", accountId);
            return query.executeUpdate();
        });
    }
    
    /**
     * Trừ tiền từ balance
     */
    public boolean deductBalance(Integer accountId, double amount) {
        return executeWithTransaction(session -> {
            // Kiểm tra balance trước khi trừ
            Account account = session.get(Account.class, accountId);
            if (account == null || account.getBalance() < amount) {
                return false;
            }
            
            Query<?> query = session.createQuery(
                "UPDATE Account a SET a.balance = a.balance - :amount WHERE a.id = :id AND a.balance >= :amount");
            query.setParameter("amount", amount);
            query.setParameter("id", accountId);
            
            int updatedRows = query.executeUpdate();
            return updatedRows > 0;
        });
    }
    
    /**
     * Đổi mật khẩu
     */
    public void changePassword(Integer accountId, String newPassword) {
        executeWithTransaction(session -> {
            Query<?> query = session.createQuery(
                "UPDATE Account a SET a.password = :password WHERE a.id = :id");
            query.setParameter("password", newPassword);
            query.setParameter("id", accountId);
            return query.executeUpdate();
        });
    }
    
    /**
     * Soft delete account (set deletedAt)
     */
    public void softDelete(Integer accountId) {
        executeWithTransaction(session -> {
            Query<?> query = session.createQuery(
                "UPDATE Account a SET a.deletedAt = CURRENT_TIMESTAMP WHERE a.id = :id");
            query.setParameter("id", accountId);
            return query.executeUpdate();
        });
    }
    
    /**
     * Tìm tất cả accounts chưa bị xóa
     */
    public List<Account> findAllActive() {
        return executeWithSession(session -> {
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.deletedAt IS NULL ORDER BY a.createdAt DESC", Account.class);
            return query.getResultList();
        });
    }
    
    /**
     * Tìm accounts theo tên (like search)
     */
    public List<Account> findByUsernameContaining(String searchTerm) {
        return executeWithSession(session -> {
            Query<Account> query = session.createQuery(
                "FROM Account a WHERE a.username LIKE :searchTerm AND a.deletedAt IS NULL ORDER BY a.username", 
                Account.class);
            query.setParameter("searchTerm", "%" + searchTerm + "%");
            return query.getResultList();
        });
    }
    
    /**
     * Kiểm tra username đã tồn tại
     */
    public boolean existsByUsername(String username) {
        return executeWithSession(session -> {
            Query<Long> query = session.createQuery(
                "SELECT COUNT(a) FROM Account a WHERE a.username = :username AND a.deletedAt IS NULL", Long.class);
            query.setParameter("username", username);
            return query.getSingleResult() > 0;
        });
    }
    
    /**
     * Đếm accounts theo role
     */
    public long countByRole(Account.Role role) {
        return executeWithSession(session -> {
            Query<Long> query = session.createQuery(
                "SELECT COUNT(a) FROM Account a WHERE a.role = :role AND a.deletedAt IS NULL", Long.class);
            query.setParameter("role", role);
            return query.getSingleResult();
        });
    }
}
