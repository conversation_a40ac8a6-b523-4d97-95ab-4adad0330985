package Repository;

import Utils.HibernateUtil;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Base Repository với các operations CRUD cơ bản và transaction management an toàn
 * @param <T> Entity type
 * @param <ID> Primary key type
 */
public abstract class BaseRepository<T, ID extends Serializable> {
    private static final Logger logger = Logger.getLogger(BaseRepository.class.getName());
    
    protected final Class<T> entityClass;
    
    protected BaseRepository(Class<T> entityClass) {
        this.entityClass = entityClass;
    }
    
    /**
     * Tìm entity theo ID
     */
    public Optional<T> findById(ID id) {
        return executeWithSession(session -> {
            T entity = session.get(entityClass, id);
            return Optional.ofNullable(entity);
        });
    }
    
    /**
     * Lưu entity mới
     */
    public T save(T entity) {
        return executeWithTransaction(session -> {
            session.persist(entity);
            return entity;
        });
    }
    
    /**
     * Cập nhật entity
     */
    public T update(T entity) {
        return executeWithTransaction(session -> {
            return session.merge(entity);
        });
    }
    
    /**
     * Lưu hoặc cập nhật entity
     */
    public T saveOrUpdate(T entity) {
        return executeWithTransaction(session -> {
            session.saveOrUpdate(entity);
            return entity;
        });
    }
    
    /**
     * Xóa entity theo ID
     */
    public void deleteById(ID id) {
        executeWithTransaction(session -> {
            T entity = session.get(entityClass, id);
            if (entity != null) {
                session.remove(entity);
            }
            return null;
        });
    }
    
    /**
     * Xóa entity
     */
    public void delete(T entity) {
        executeWithTransaction(session -> {
            session.remove(entity);
            return null;
        });
    }
    
    /**
     * Tìm tất cả entities
     */
    public List<T> findAll() {
        return executeWithSession(session -> {
            Query<T> query = session.createQuery("FROM " + entityClass.getSimpleName(), entityClass);
            return query.getResultList();
        });
    }
    
    /**
     * Đếm tổng số entities
     */
    public long count() {
        return executeWithSession(session -> {
            Query<Long> query = session.createQuery("SELECT COUNT(e) FROM " + entityClass.getSimpleName() + " e", Long.class);
            return query.getSingleResult();
        });
    }
    
    /**
     * Kiểm tra entity có tồn tại theo ID
     */
    public boolean existsById(ID id) {
        return executeWithSession(session -> {
            Query<Long> query = session.createQuery(
                "SELECT COUNT(e) FROM " + entityClass.getSimpleName() + " e WHERE e.id = :id", Long.class);
            query.setParameter("id", id);
            return query.getSingleResult() > 0;
        });
    }
    
    /**
     * Thực thi với session (read-only operations)
     */
    protected <R> R executeWithSession(Function<Session, R> operation) {
        Session session = null;
        try {
            session = HibernateUtil.openSession();
            return operation.apply(session);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error executing session operation", e);
            throw new RuntimeException("Database operation failed", e);
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Thực thi với transaction (write operations)
     */
    protected <R> R executeWithTransaction(Function<Session, R> operation) {
        Session session = null;
        Transaction transaction = null;
        try {
            session = HibernateUtil.openSession();
            transaction = session.beginTransaction();
            
            R result = operation.apply(session);
            
            transaction.commit();
            return result;
            
        } catch (Exception e) {
            if (transaction != null && transaction.isActive()) {
                try {
                    transaction.rollback();
                } catch (Exception rollbackEx) {
                    logger.log(Level.SEVERE, "Error rolling back transaction", rollbackEx);
                }
            }
            logger.log(Level.SEVERE, "Error executing transaction operation", e);
            throw new RuntimeException("Database transaction failed", e);
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Thực thi void operation với transaction
     */
    protected void executeVoidWithTransaction(Consumer<Session> operation) {
        executeWithTransaction(session -> {
            operation.accept(session);
            return null;
        });
    }
    
    /**
     * Flush và clear session
     */
    protected void flushAndClear(Session session) {
        session.flush();
        session.clear();
    }
    
    /**
     * Tạo named query
     */
    protected <R> Query<R> createNamedQuery(String queryName, Class<R> resultClass) {
        Session session = HibernateUtil.getCurrentSession();
        return session.createNamedQuery(queryName, resultClass);
    }
    
    /**
     * Tạo native query
     */
    protected <R> Query<R> createNativeQuery(String sql, Class<R> resultClass) {
        Session session = HibernateUtil.getCurrentSession();
        return session.createNativeQuery(sql, resultClass);
    }
}
